<!DOCTYPE html>
<html th:lang="${#locale.language}" xmlns:th="http://www.thymeleaf.org">
<head>
    <title>User List Fragment</title>
</head>
<body>

<div th:fragment="userList"
     id="userList"
     hx-trigger="refresh from:body"
     hx-get="/admin/users/list"
     hx-swap="outerHTML"
     th:hx-vals="|{'page': '${currentPage}', 'fullName': '${fullNameFilter}', 'email': '${emailFilter}', 'company': '${companyFilter}', 'role': '${roleFilter}', 'enabled': '${enabledFilter}', 'accountLocked': '${accountLockedFilter}'}|">

    <!-- Hidden input to store current page for refresh functionality -->
    <input type="hidden" id="currentPageInput" name="page" th:value="${currentPage}">
    <table class="table table-hover">
        <thead>
            <!-- Filter Row -->
            <tr>
                <th class="text-truncate align-middle" style="width: 20%;">
                    <!-- Full Name Filter -->
                    <input type="text"
                           id="fullNameFilter"
                           name="fullName"
                           class="form-control form-control-sm"
                           th:placeholder="#{user.filter.fullName}"
                           th:value="${fullNameFilter}"
                           hx-get="/admin/users/list"
                           hx-target="#userList"
                           hx-swap="outerHTML"
                           hx-trigger="change, keyup delay:500ms"
                           hx-include="#emailFilter, #companyFilter, #roleFilter, #enabledFilter, #accountLockedFilter"
                           hx-vals='{"page": "1"}'
                           hx-indicator="#loading-indicator">
                </th>
                <th class="text-truncate align-middle" style="width: 25%;">
                    <!-- Email Filter -->
                    <input type="text"
                           id="emailFilter"
                           name="email"
                           class="form-control form-control-sm"
                           th:placeholder="#{user.filter.email}"
                           th:value="${emailFilter}"
                           hx-get="/admin/users/list"
                           hx-target="#userList"
                           hx-swap="outerHTML"
                           hx-trigger="change, keyup delay:500ms"
                           hx-include="#fullNameFilter, #companyFilter, #roleFilter, #enabledFilter, #accountLockedFilter"
                           hx-vals='{"page": "1"}'
                           hx-indicator="#loading-indicator">
                </th>
                <th class="text-truncate align-middle" style="width: 15%;">
                    <!-- Company Filter -->
                    <select id="companyFilter"
                            name="company"
                            class="form-select form-select-sm"
                            hx-get="/admin/users/list"
                            hx-target="#userList"
                            hx-swap="outerHTML"
                            hx-trigger="change"
                            hx-include="#fullNameFilter, #emailFilter, #roleFilter, #enabledFilter, #accountLockedFilter"
                            hx-vals='{"page": "1"}'
                            hx-indicator="#loading-indicator">
                        <option value="" th:text="#{user.filter.company.all}">All Companies</option>
                        <option value="NO_COMPANY"
                                th:text="#{user.filter.company.none}"
                                th:selected="${companyFilter != null && companyFilter.equals('NO_COMPANY')}">Without Company</option>
                        <option th:each="comp : ${allCompanies}"
                                th:value="${comp}"
                                th:text="${comp}"
                                th:selected="${companyFilter != null && companyFilter.equals(comp)}">Company</option>
                    </select>
                </th>
                <th class="text-truncate align-middle" style="width: 10%;">
                    <!-- Status Filter -->
                    <select id="enabledFilter"
                            name="enabled"
                            class="form-select form-select-sm"
                            hx-get="/admin/users/list"
                            hx-target="#userList"
                            hx-swap="outerHTML"
                            hx-trigger="change"
                            hx-include="#fullNameFilter, #emailFilter, #companyFilter, #roleFilter, #accountLockedFilter"
                            hx-vals='{"page": "1"}'
                            hx-indicator="#loading-indicator">
                        <option value="" th:text="#{user.filter.status.all}">All Status</option>
                        <option value="true" th:selected="${enabledFilter != null && enabledFilter}" th:text="#{user.status.active}">Active</option>
                        <option value="false" th:selected="${enabledFilter != null && !enabledFilter}" th:text="#{user.status.inactive}">Inactive</option>
                    </select>
                </th>
                <th class="text-truncate align-middle" style="width: 10%;">
                    <!-- Lock Status Filter -->
                    <select id="accountLockedFilter"
                            name="accountLocked"
                            class="form-select form-select-sm"
                            hx-get="/admin/users/list"
                            hx-target="#userList"
                            hx-swap="outerHTML"
                            hx-trigger="change"
                            hx-include="#fullNameFilter, #emailFilter, #companyFilter, #roleFilter, #enabledFilter"
                            hx-vals='{"page": "1"}'
                            hx-indicator="#loading-indicator">
                        <option value="" th:text="#{user.filter.lock.all}">All Locks</option>
                        <option value="false" th:selected="${accountLockedFilter != null && !accountLockedFilter}" th:text="#{user.status.unlocked}">Unlocked</option>
                        <option value="true" th:selected="${accountLockedFilter != null && accountLockedFilter}" th:text="#{user.status.locked}">Locked</option>
                    </select>
                </th>
                <th class="text-truncate align-middle" style="width: 10%;">
                    <!-- Role Filter -->
                    <select id="roleFilter"
                            name="role"
                            class="form-select form-select-sm"
                            hx-get="/admin/users/list"
                            hx-target="#userList"
                            hx-swap="outerHTML"
                            hx-trigger="change"
                            hx-include="#fullNameFilter, #emailFilter, #companyFilter, #enabledFilter, #accountLockedFilter"
                            hx-vals='{"page": "1"}'
                            hx-indicator="#loading-indicator">
                        <option value="" th:text="#{user.filter.role.all}">All Roles</option>
                        <option th:each="role : ${allRoles}"
                                th:value="${role}"
                                th:text="${#strings.replace(role, 'ROLE_', '')}"
                                th:selected="${roleFilter != null && roleFilter.equals(role.toString())}">Role</option>
                    </select>
                </th>
                <th class="text-truncate align-middle" style="width: 10%;">
                    <!-- Clear Filters Button -->
                    <button type="button"
                            class="btn btn-sm btn-outline-secondary"
                            hx-get="/admin/users/clearFilters"
                            hx-target="#userList"
                            hx-swap="outerHTML"
                            hx-indicator="#loading-indicator"
                            th:title="#{user.button.clear.filters}">
                        <i class="ti ti-filter-off"></i>
                    </button>
                </th>
            </tr>
        </thead>
        <tbody id="userListBody">
            <tr th:if="${users.empty}">
                <td colspan="7" class="text-center text-muted py-4">
                    <i class="ti ti-users ti-lg mb-2"></i>
                    <div th:text="#{user.list.empty}">No users found</div>
                </td>
            </tr>
            <tr th:each="user : ${users.content}" class="user-row">
                <td>
                    <div class="d-flex align-items-center">
                        <div class="avatar avatar-sm me-3">
                            <img alt="Avatar"
                                 class="rounded-circle"
                                 th:src="${user.photo != null} ? ${'/images/' + user.photo} : '../../assets/img/avatars/silhouette.png'">
                        </div>
                        <div>
                            <h6 class="mb-0" th:text="${user.fullName}">Full Name</h6>
                            <small class="text-muted" th:text="${user.position ?: 'No position'}">Position</small>
                        </div>
                    </div>
                </td>
                <td>
                    <span th:text="${user.email}"><EMAIL></span>
                    <br>
                    <small class="text-muted" th:text="${user.phone ?: 'No phone'}">Phone</small>
                </td>
                <td>
                    <span th:text="${user.company ?: 'No company'}">Company</span>
                    <br>
                    <small class="text-muted" th:text="${user.city ?: 'No city'}">City</small>
                </td>
                <td>
                    <span th:if="${user.enabled}" class="badge bg-label-success" th:text="#{user.status.active}">Active</span>
                    <span th:unless="${user.enabled}" class="badge bg-label-secondary" th:text="#{user.status.inactive}">Inactive</span>
                </td>
                <td>
                    <span th:if="${user.accountLocked}" class="badge bg-label-danger">
                        <i class="ti ti-lock ti-xs"></i> <span th:text="#{user.status.locked}">Locked</span>
                    </span>
                    <span th:unless="${user.accountLocked}" class="badge bg-label-success">
                        <i class="ti ti-lock-open ti-xs"></i> <span th:text="#{user.status.unlocked}">Unlocked</span>
                    </span>
                </td>
                <td>
                    <div th:each="role : ${user.roles}" class="d-inline">
                        <span class="badge bg-label-info me-1"
                              th:text="${#strings.replace(role.name, 'ROLE_', '')}"
                              th:classappend="${role.name == 'ROLE_ADMIN'} ? 'bg-label-danger' : (${role.name == 'ROLE_MODERATOR'} ? 'bg-label-warning' : 'bg-label-info')">
                            Role
                        </span>
                    </div>
                </td>
                <td>
                    <div class="dropdown">
                        <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                            <i class="ti ti-dots-vertical"></i>
                        </button>
                        <div class="dropdown-menu">
                            <a class="dropdown-item"
                               href="javascript:void(0);"
                               th:hx-get="@{/admin/users/form(id=${user.id})}"
                               hx-target="#userFormOffcanvas .offcanvas-body"
                               hx-swap="innerHTML"
                               hx-trigger="click"
                               data-bs-toggle="offcanvas"
                               data-bs-target="#userFormOffcanvas"
                               aria-controls="userFormOffcanvas"
                               th:attr="data-user-id=${user.id}, data-title=#{user.button.edit}">
                                <i class="ti ti-pencil me-1"></i> <span th:text="#{user.button.edit}">Edit</span>
                            </a>

                            <!-- Activate/Deactivate User -->
                            <a th:if="${user.enabled}"
                               class="dropdown-item text-warning"
                               href="javascript:void(0);"
                               th:hx-put="@{/admin/users/deactivate/{email}(email=${user.email})}"
                               hx-swap="none"
                               th:hx-confirm="#{user.deactivate.confirm}"
                               hx-on::after-request="if(event.detail.xhr.status === 200) { refreshUserListWithCurrentState(); }">
                                <i class="ti ti-user-off me-1"></i> <span th:text="#{user.button.deactivate}">Deactivate</span>
                            </a>
                            <a th:if="${!user.enabled}"
                               class="dropdown-item text-success"
                               href="javascript:void(0);"
                               th:hx-put="@{/admin/users/activate/{email}(email=${user.email})}"
                               hx-swap="none"
                               th:hx-confirm="#{user.activate.confirm}"
                               hx-on::after-request="if(event.detail.xhr.status === 200) { refreshUserListWithCurrentState(); }">
                                <i class="ti ti-user-check me-1"></i> <span th:text="#{user.button.activate}">Activate</span>
                            </a>

                            <!-- Lock/Unlock User -->
                            <a th:if="${!user.accountLocked}"
                               class="dropdown-item text-danger"
                               href="javascript:void(0);"
                               th:hx-put="@{/admin/users/lock/{email}(email=${user.email})}"
                               hx-swap="none"
                               th:hx-confirm="#{user.lock.confirm}"
                               hx-on::after-request="if(event.detail.xhr.status === 200) { refreshUserListWithCurrentState(); }">
                                <i class="ti ti-lock me-1"></i> <span th:text="#{user.button.lock}">Lock</span>
                            </a>
                            <a th:if="${user.accountLocked}"
                               class="dropdown-item text-info"
                               href="javascript:void(0);"
                               th:hx-put="@{/admin/users/unlock/{email}(email=${user.email})}"
                               hx-swap="none"
                               th:hx-confirm="#{user.unlock.confirm}"
                               hx-on::after-request="if(event.detail.xhr.status === 200) { refreshUserListWithCurrentState(); }">
                                <i class="ti ti-lock-open me-1"></i> <span th:text="#{user.button.unlock}">Unlock</span>
                            </a>
                        </div>
                    </div>
                </td>
            </tr>

            <!-- Empty placeholder rows to maintain consistent table height (10 rows total) -->
            <tr th:each="i : ${#numbers.sequence(1, 10 - users.numberOfElements)}"
                th:if="${users.numberOfElements < 10 && !users.empty}"
                class="placeholder-row">
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
            </tr>
        </tbody>
    </table>

    <!-- Pagination -->
    <div th:if="${!users.empty}" class="d-flex justify-content-between align-items-center mt-3 mb-3 px-5">
        <div class="text-muted">
            <span th:text="#{table.pagination.showing.from}">Showing from</span>
            <span th:text="${users.number * users.size + 1}">1</span>
            <span th:text="#{table.pagination.to}">to</span>
            <span th:text="${users.number * users.size + users.numberOfElements}">10</span>
            <span th:text="#{table.pagination.of.total}">of total</span>
            <span th:text="${users.totalElements}">100</span>
            <span th:text="#{table.pagination.entries}">entries</span>
        </div>

        <nav th:aria-label="#{user.pagination.label}" th:if="${users.totalPages > 1}">
            <ul class="pagination pagination-rounded pagination-sm mb-0">
                <!-- First Page Button -->
                <li class="paginate_button page-item first" th:classappend="${users.first} ? 'disabled' : ''">
                    <a href="#"
                       class="page-link"
                       th:hx-get="@{/admin/users/list(page=1)}"
                       hx-target="#userList"
                       hx-swap="outerHTML"
                       hx-include="#fullNameFilter, #emailFilter, #companyFilter, #roleFilter, #enabledFilter, #accountLockedFilter"
                       hx-indicator="#loading-indicator">
                        <i class="ti ti-chevrons-left ti-xs"></i>
                    </a>
                </li>

                <!-- Previous Button -->
                <li class="paginate_button page-item previous" th:classappend="${!users.hasPrevious()} ? 'disabled' : ''">
                    <a href="#"
                       class="page-link"
                       th:hx-get="@{/admin/users/list(page=${currentPage - 1})}"
                       hx-target="#userList"
                       hx-swap="outerHTML"
                       hx-include="#fullNameFilter, #emailFilter, #companyFilter, #roleFilter, #enabledFilter, #accountLockedFilter"
                       hx-indicator="#loading-indicator">
                        <i class="ti ti-chevron-left ti-xs"></i>
                    </a>
                </li>

                <!-- If total pages <= 5, show all pages -->
                <th:block th:if="${users.totalPages <= 5}">
                    <th:block th:each="index : ${#numbers.sequence(1, users.totalPages, 1)}">
                        <li class="paginate_button page-item" th:classappend="${index == currentPage} ? 'active' : ''">
                            <a href="#"
                               class="page-link"
                               th:text="${index}"
                               th:hx-get="@{/admin/users/list(page=${index})}"
                               hx-target="#userList"
                               hx-swap="outerHTML"
                               hx-include="#fullNameFilter, #emailFilter, #companyFilter, #roleFilter, #enabledFilter, #accountLockedFilter">
                            </a>
                        </li>
                    </th:block>
                </th:block>

                <!-- If total pages > 5, use smart pagination -->
                <th:block th:if="${users.totalPages > 5}">
                    <!-- Case 1: currentPage in first 5 pages - Show first 5 pages, ellipsis, last page -->
                    <th:block th:if="${currentPage <= 5}">
                        <!-- First 5 pages -->
                        <th:block th:each="index : ${#numbers.sequence(1, 5, 1)}">
                            <li class="paginate_button page-item" th:classappend="${index == currentPage} ? 'active' : ''">
                                <a href="#"
                                   class="page-link"
                                   th:text="${index}"
                                   th:hx-get="@{/admin/users/list(page=${index})}"
                                   hx-target="#userList"
                                   hx-swap="outerHTML"
                                   hx-include="#fullNameFilter, #emailFilter, #companyFilter, #roleFilter, #enabledFilter, #accountLockedFilter">
                                </a>
                            </li>
                        </th:block>

                        <!-- Ellipsis if needed -->
                        <li class="paginate_button page-item disabled" th:if="${users.totalPages > 6}">
                            <span class="page-link">...</span>
                        </li>

                        <!-- Last page -->
                        <li class="paginate_button page-item" th:if="${users.totalPages > 5}" th:classappend="${users.totalPages == currentPage} ? 'active' : ''">
                            <a href="#"
                               class="page-link"
                               th:text="${users.totalPages}"
                               th:hx-get="@{/admin/users/list(page=${users.totalPages})}"
                               hx-target="#userList"
                               hx-swap="outerHTML"
                               hx-include="#fullNameFilter, #emailFilter, #companyFilter, #roleFilter, #enabledFilter, #accountLockedFilter">
                            </a>
                        </li>
                    </th:block>

                    <!-- Case 2: currentPage in middle segment - Show page 1, ellipsis, pages around current, ellipsis, last page -->
                    <th:block th:if="${currentPage > 5 && currentPage < users.totalPages - 4}">
                        <!-- First page -->
                        <li class="paginate_button page-item" th:classappend="${1 == currentPage} ? 'active' : ''">
                            <a href="#"
                               class="page-link"
                               th:text="1"
                               th:hx-get="@{/admin/users/list(page=1)}"
                               hx-target="#userList"
                               hx-swap="outerHTML"
                               hx-include="#fullNameFilter, #emailFilter, #companyFilter, #roleFilter, #enabledFilter, #accountLockedFilter">
                            </a>
                        </li>

                        <!-- Ellipsis -->
                        <li class="paginate_button page-item disabled">
                            <span class="page-link">...</span>
                        </li>

                        <!-- Pages around current page (current-1, current, current+1) -->
                        <th:block th:each="index : ${#numbers.sequence(currentPage - 1, currentPage + 1, 1)}">
                            <li class="paginate_button page-item" th:classappend="${index == currentPage} ? 'active' : ''">
                                <a href="#"
                                   class="page-link"
                                   th:text="${index}"
                                   th:hx-get="@{/admin/users/list(page=${index})}"
                                   hx-target="#userList"
                                   hx-swap="outerHTML"
                                   hx-include="#fullNameFilter, #emailFilter, #companyFilter, #roleFilter, #enabledFilter, #accountLockedFilter">
                                </a>
                            </li>
                        </th:block>

                        <!-- Ellipsis -->
                        <li class="paginate_button page-item disabled">
                            <span class="page-link">...</span>
                        </li>

                        <!-- Last page -->
                        <li class="paginate_button page-item" th:classappend="${users.totalPages == currentPage} ? 'active' : ''">
                            <a href="#"
                               class="page-link"
                               th:text="${users.totalPages}"
                               th:hx-get="@{/admin/users/list(page=${users.totalPages})}"
                               hx-target="#userList"
                               hx-swap="outerHTML"
                               hx-include="#fullNameFilter, #emailFilter, #companyFilter, #roleFilter, #enabledFilter, #accountLockedFilter">
                            </a>
                        </li>
                    </th:block>

                    <!-- Case 3: currentPage in last 5 pages - Show page 1, ellipsis, last 5 pages -->
                    <th:block th:if="${currentPage >= users.totalPages - 4}">
                        <!-- First page -->
                        <li class="paginate_button page-item" th:classappend="${1 == currentPage} ? 'active' : ''">
                            <a href="#"
                               class="page-link"
                               th:text="1"
                               th:hx-get="@{/admin/users/list(page=1)}"
                               hx-target="#userList"
                               hx-swap="outerHTML"
                               hx-include="#fullNameFilter, #emailFilter, #companyFilter, #roleFilter, #enabledFilter, #accountLockedFilter">
                            </a>
                        </li>

                        <!-- Ellipsis if needed -->
                        <li class="paginate_button page-item disabled" th:if="${users.totalPages > 6}">
                            <span class="page-link">...</span>
                        </li>

                        <!-- Last 5 pages -->
                        <th:block th:each="index : ${#numbers.sequence(users.totalPages - 4, users.totalPages, 1)}">
                            <li class="paginate_button page-item" th:classappend="${index == currentPage} ? 'active' : ''">
                                <a href="#"
                                   class="page-link"
                                   th:text="${index}"
                                   th:hx-get="@{/admin/users/list(page=${index})}"
                                   hx-target="#userList"
                                   hx-swap="outerHTML"
                                   hx-include="#fullNameFilter, #emailFilter, #companyFilter, #roleFilter, #enabledFilter, #accountLockedFilter">
                                </a>
                            </li>
                        </th:block>
                    </th:block>
                </th:block>

                <!-- Next Button -->
                <li class="paginate_button page-item next" th:classappend="${!users.hasNext()} ? 'disabled' : ''">
                    <a href="#"
                       class="page-link"
                       th:hx-get="@{/admin/users/list(page=${currentPage + 1})}"
                       hx-target="#userList"
                       hx-swap="outerHTML"
                       hx-include="#fullNameFilter, #emailFilter, #companyFilter, #roleFilter, #enabledFilter, #accountLockedFilter"
                       hx-indicator="#loading-indicator">
                        <i class="ti ti-chevron-right ti-xs"></i>
                    </a>
                </li>

                <!-- Last Page Button -->
                <li class="paginate_button page-item last" th:classappend="${users.last} ? 'disabled' : ''">
                    <a href="#"
                       class="page-link"
                       th:hx-get="@{/admin/users/list(page=${users.totalPages})}"
                       hx-target="#userList"
                       hx-swap="outerHTML"
                       hx-include="#fullNameFilter, #emailFilter, #companyFilter, #roleFilter, #enabledFilter, #accountLockedFilter"
                       hx-indicator="#loading-indicator">
                        <i class="ti ti-chevrons-right ti-xs"></i>
                    </a>
                </li>
            </ul>
        </nav>
    </div>
</div>

</body>
</html>
