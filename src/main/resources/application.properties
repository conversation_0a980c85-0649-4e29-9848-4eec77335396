server.port=8080

## Database configuration
spring.datasource.url=${SPRING_DATASOURCE_URL}
spring.datasource.username=${SPRING_DATASOURCE_USERNAME}
spring.datasource.password=${SPRING_DATASOURCE_PASSWORD}
spring.datasource.driverClassName=${SPRING_DRIVER_CLASS}

## Hibernate properties
spring.jpa.hibernate.ddl-auto=update
spring.jpa.open-in-view=false

## HikariCP connection pool settings
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.max-lifetime=600000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.validation-timeout=5000
spring.datasource.hikari.connection-test-query=SELECT 1
spring.datasource.hikari.register-mbeans=true

## SQL Server specific settings
spring.datasource.hikari.data-source-properties.socketTimeout=60000
spring.datasource.hikari.data-source-properties.loginTimeout=30000
spring.datasource.hikari.data-source-properties.connectTimeout=30000
spring.datasource.hikari.data-source-properties.queryTimeout=30000
spring.datasource.hikari.data-source-properties.tcpKeepAlive=true

#spring.billing-datasource.maxLifetime=54000

## Server connection timeout settings
server.tomcat.connection-timeout=30000
server.tomcat.max-connections=10000
server.tomcat.accept-count=100
server.tomcat.max-keep-alive-requests=100
server.tomcat.keep-alive-timeout=30000

## Session management optimization
## Multiple concurrent sessions are allowed (configured in SecurityConfiguration)
server.servlet.session.timeout=30m
server.servlet.session.cookie.secure=false
server.servlet.session.cookie.http-only=true
server.servlet.session.cookie.same-site=lax

## Load Initial Data from Runners to Database (Disable on Prod)
load-data-on-startup=false

## Errors
server.error.whitelabel.enabled=true
server.error.path=/error
server.error.include-binding-errors=always
server.error.include-exception=true
server.error.include-message=always
server.error.include-stacktrace=always

## File Upload
spring.servlet.multipart.max-file-size = 5MB
spring.servlet.multipart.max-request-size = 5MB

## JWT secret key
security.jwt.secret-key =${JWT_SECRET_KEY}
security.jwt.expiration-time=3600000
security.jwt.refresh-expiration-time=86400000
security.jwt.name=${JWT_NAME}

## Mail properties
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=${SUPPORT_EMAIL}
spring.mail.password=${JOB_PASSWORD}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

spring.config.import=optional:file:.env[.properties]

## Disable Thymeleaf caching for development, should be enabled for production
spring.thymeleaf.cache=false

## Google reCAPTCHA configuration
google.recaptcha.enabled=true
google.recaptcha.site-key=${RECAPTCHA_SITE_KEY}
google.recaptcha.secret-key=${RECAPTCHA_SECRET_KEY}
google.recaptcha.threshold=0.5

## Password policy configuration
security.password.history-count=5
security.password.expiry-days=90

## Application configuration
application.base-url=http://localhost:8080

## SecureRandom optimization
# Platform-specific optimizations are now handled in EntropyConfiguration.java
# and SecureRandomConfig.java to provide better performance on both Windows and Unix systems
