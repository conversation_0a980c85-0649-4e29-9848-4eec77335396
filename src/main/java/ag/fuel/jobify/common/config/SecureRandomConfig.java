package ag.fuel.jobify.common.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

/**
 * Configuration class to optimize SecureRandom performance.
 * This helps reduce the SecureRandom initialization time that can cause
 * warnings like "Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [102] milliseconds"
 */
@Configuration
public class SecureRandomConfig {

    private static final Logger LOGGER = LoggerFactory.getLogger(SecureRandomConfig.class);
    private static final boolean IS_WINDOWS = System.getProperty("os.name").toLowerCase().contains("win");

    /**
     * Creates an optimized SecureRandom bean for better performance.
     * Uses platform-specific optimizations:
     * - Windows-PRNG for Windows systems
     * - NativePRNG for Unix-like systems
     * Falls back to DRBG or default with pre-seeding if specialized algorithms are not available.
     * 
     * @return Optimized SecureRandom instance
     */
    @Bean
    public SecureRandom secureRandom() {
        if (IS_WINDOWS) {
            return createWindowsOptimizedSecureRandom();
        } else {
            return createUnixOptimizedSecureRandom();
        }
    }

    /**
     * Creates a SecureRandom instance optimized for Windows systems.
     * 
     * @return Windows-optimized SecureRandom instance
     */
    private SecureRandom createWindowsOptimizedSecureRandom() {
        try {
            // Try Windows-PRNG first (available in newer JDK versions on Windows)
            SecureRandom secureRandom = SecureRandom.getInstance("Windows-PRNG");
            LOGGER.info("SecureRandom initialized with Windows-PRNG algorithm for better performance");
            return secureRandom;
        } catch (NoSuchAlgorithmException e) {
            try {
                // Try DRBG (available in Java 9+)
                SecureRandom secureRandom = SecureRandom.getInstance("DRBG");
                LOGGER.info("SecureRandom initialized with DRBG algorithm for better performance");
                return secureRandom;
            } catch (NoSuchAlgorithmException e2) {
                // Fall back to default with pre-seeding
                return createDefaultSecureRandom();
            }
        }
    }

    /**
     * Creates a SecureRandom instance optimized for Unix-like systems.
     * 
     * @return Unix-optimized SecureRandom instance
     */
    private SecureRandom createUnixOptimizedSecureRandom() {
        try {
            // Try to use NativePRNG which is faster on Unix-like systems
            SecureRandom secureRandom = SecureRandom.getInstance("NativePRNG");
            LOGGER.info("SecureRandom initialized with NativePRNG algorithm for better performance");
            return secureRandom;
        } catch (NoSuchAlgorithmException e) {
            try {
                // Fall back to NativePRNGNonBlocking if available
                SecureRandom secureRandom = SecureRandom.getInstance("NativePRNGNonBlocking");
                LOGGER.info("SecureRandom initialized with NativePRNGNonBlocking algorithm for better performance");
                return secureRandom;
            } catch (NoSuchAlgorithmException e2) {
                // Fall back to default with pre-seeding
                return createDefaultSecureRandom();
            }
        }
    }

    /**
     * Creates a default SecureRandom instance with pre-seeding for better performance.
     * 
     * @return Pre-seeded default SecureRandom instance
     */
    private SecureRandom createDefaultSecureRandom() {
        try {
            // Try SHA1PRNG explicitly with pre-seeding
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            // Pre-seed to avoid blocking on first use
            secureRandom.nextBytes(new byte[20]);
            LOGGER.info("SecureRandom initialized with SHA1PRNG algorithm and pre-seeded for better performance");
            return secureRandom;
        } catch (NoSuchAlgorithmException e) {
            // Use default SecureRandom with optimizations
            SecureRandom secureRandom = new SecureRandom();
            // Pre-seed to avoid blocking on first use
            secureRandom.nextBytes(new byte[20]);
            LOGGER.info("SecureRandom initialized with default algorithm and pre-seeded for better performance");
            return secureRandom;
        }
    }
}
