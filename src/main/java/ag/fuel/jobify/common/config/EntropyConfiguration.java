package ag.fuel.jobify.common.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;

/**
 * Configuration class to optimize entropy sources for SecureRandom.
 * This helps reduce SecureRandom initialization time and improves performance.
 * Provides platform-specific optimizations for both Windows and Unix-like systems.
 */
@Configuration
public class EntropyConfiguration {

    private static final Logger LOGGER = LoggerFactory.getLogger(EntropyConfiguration.class);
    private static final boolean IS_WINDOWS = System.getProperty("os.name").toLowerCase().contains("win");

    /**
     * Sets system properties to optimize SecureRandom performance.
     * This is done at startup to ensure all SecureRandom instances benefit from the optimization.
     * Uses platform-specific settings for Windows and Unix-like systems.
     */
    @PostConstruct
    public void configureEntropy() {
        try {
            if (IS_WINDOWS) {
                configureWindowsEntropy();
            } else {
                configureUnixEntropy();
            }

            LOGGER.info("Entropy configuration completed successfully for {} platform", 
                    IS_WINDOWS ? "Windows" : "Unix-like");
        } catch (Exception e) {
            LOGGER.warn("Failed to configure entropy sources: {}", e.getMessage());
        }
    }

    /**
     * Configures entropy sources optimized for Windows systems.
     * Sets JVM properties to improve SecureRandom performance on Windows.
     */
    private void configureWindowsEntropy() {
        // On Windows, set properties to prefer faster algorithms

        // Set JVM to prefer SHA1PRNG with faster seeding
        String secureRandomAlgorithm = System.getProperty("securerandom.algorithm");
        if (secureRandomAlgorithm == null || secureRandomAlgorithm.isEmpty()) {
            // On Windows, SHA1PRNG is typically used, but we want to ensure it's pre-seeded
            System.setProperty("securerandom.algorithm", "SHA1PRNG");
            LOGGER.info("Set securerandom.algorithm to SHA1PRNG for Windows");
        } else {
            LOGGER.info("SecureRandom algorithm already configured: {}", secureRandomAlgorithm);
        }

        // Set property to reduce blocking during initialization
        System.setProperty("java.security.egd", "file:/dev/random");
        LOGGER.info("Set java.security.egd for Windows optimization");
    }

    /**
     * Configures entropy sources optimized for Unix-like systems.
     * Sets JVM properties to improve SecureRandom performance on Linux, macOS, etc.
     */
    private void configureUnixEntropy() {
        // Set the entropy gathering device to use /dev/urandom for better performance
        // This is particularly important on Linux systems where /dev/random can block
        String entropySource = System.getProperty("java.security.egd");
        if (entropySource == null || entropySource.isEmpty()) {
            System.setProperty("java.security.egd", "file:/dev/./urandom");
            LOGGER.info("Set java.security.egd to file:/dev/./urandom for better SecureRandom performance");
        } else {
            LOGGER.info("Entropy source already configured: {}", entropySource);
        }

        // Set SecureRandom algorithm preference for better performance
        String secureRandomSource = System.getProperty("securerandom.source");
        if (secureRandomSource == null || secureRandomSource.isEmpty()) {
            System.setProperty("securerandom.source", "file:/dev/urandom");
            LOGGER.info("Set securerandom.source to file:/dev/urandom for better performance");
        } else {
            LOGGER.info("SecureRandom source already configured: {}", secureRandomSource);
        }
    }
}
