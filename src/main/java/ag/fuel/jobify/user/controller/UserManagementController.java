package ag.fuel.jobify.user.controller;

import ag.fuel.jobify.auth.entity.ERole;
import ag.fuel.jobify.auth.entity.Role;
import ag.fuel.jobify.common.dto.GenericModalMessage;
import ag.fuel.jobify.common.util.Constants;
import ag.fuel.jobify.company.entity.Company;
import ag.fuel.jobify.company.service.CompanyService;
import ag.fuel.jobify.reference.entity.Country;
import ag.fuel.jobify.reference.entity.Language;
import ag.fuel.jobify.reference.entity.WorkFormat;
import ag.fuel.jobify.reference.service.CountryService;
import ag.fuel.jobify.reference.service.LanguageService;
import ag.fuel.jobify.reference.service.WorkFormatService;
import ag.fuel.jobify.user.dto.CreateUserDto;
import ag.fuel.jobify.user.dto.EditUserDto;
import ag.fuel.jobify.user.dto.UserListDto;
import ag.fuel.jobify.user.entity.User;
import ag.fuel.jobify.user.service.UserService;
import jakarta.servlet.http.HttpServletRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.MessageSource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/admin/users")
@RequiredArgsConstructor
@Tag(name = "User Management", description = "Admin user management operations")
public class UserManagementController {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserManagementController.class);

    private final UserService userService;
    private final CountryService countryService;
    private final LanguageService languageService;
    private final WorkFormatService workFormatService;
    private final CompanyService companyService;
    private final MessageSource messageSource;

    /**
     * Exclude id field from binding for create operations
     */
    @InitBinder("createUserDto")
    public void initCreateUserBinder(WebDataBinder binder) {
        binder.setDisallowedFields("id");
    }

    /**
     * Main user management page
     */
    @GetMapping
    @Operation(summary = "Get user management page", description = "Display the main user management page")
    public String userManagementPage(Model model) {
        LOGGER.debug("Accessing user management page");

        // Get initial data for the page
        Page<User> users = userService.getUsersWithFilters(null, null, null, null, null, null,
                PageRequest.of(0, 10, Sort.by("fullName").ascending()));
        List<String> countries = userService.getDistinctCountries();
        List<String> companies = userService.getDistinctCompanies();
        List<ERole> roles = userService.getDistinctRoles();

        // Get user statistics for cards
        long totalUsers = userService.getTotalUsersCount();
        long activeUsers = userService.getActiveUsersCount();
        long inactiveUsers = userService.getInactiveUsersCount();
        long lockedUsers = userService.getLockedUsersCount();
        long usersWithoutCompany = userService.getUsersWithoutCompanyCount();

        model.addAttribute("users", users);
        model.addAttribute("currentPage", 1); // Initial page is always 1
        model.addAttribute("countries", countries);
        model.addAttribute("companies", companies);
        model.addAttribute("roles", roles);
        model.addAttribute("allCompanies", companies);
        model.addAttribute("allRoles", roles);
        model.addAttribute("createUserDto", new CreateUserDto("", "", "", null, "", "", "", "", "", "", "", false, null));

        // Add statistics for cards
        model.addAttribute("totalUsersCount", totalUsers);
        model.addAttribute("activeUsersCount", activeUsers);
        model.addAttribute("inactiveUsersCount", inactiveUsers);
        model.addAttribute("lockedUsersCount", lockedUsers);
        model.addAttribute("usersWithoutCompanyCount", usersWithoutCompany);

        return "user/user-management";
    }

    /**
     * Get user list with filtering (HTMX endpoint)
     */
    @GetMapping("/list")
    @Operation(summary = "Get filtered user list", description = "Get paginated and filtered user list")
    public String getUserList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(required = false) String fullName,
            @RequestParam(required = false) String email,
            @RequestParam(required = false) String company,
            @RequestParam(required = false) String role,
            @RequestParam(required = false) Boolean enabled,
            @RequestParam(required = false) Boolean accountLocked,
            Model model) {

        LOGGER.debug("Getting user list - page: {}, fullName: {}, email: {}, company: {}, role: {}, enabled: {}, accountLocked: {}",
                page, fullName, email, company, role, enabled, accountLocked);

        // Convert role string to ERole enum
        ERole roleEnum = null;
        if (role != null && !role.trim().isEmpty()) {
            try {
                roleEnum = ERole.valueOf(role);
            } catch (IllegalArgumentException e) {
                LOGGER.warn("Invalid role parameter: {}", role);
            }
        }

        Pageable pageable = PageRequest.of(page - 1, 10, Sort.by("fullName").ascending());
        Page<User> users = userService.getUsersWithFilters(fullName, email, company, roleEnum, enabled, accountLocked, pageable);

        // Get filter data for dropdowns
        List<String> companies = userService.getDistinctCompanies();
        List<ERole> roles = userService.getDistinctRoles();

        model.addAttribute("users", users);
        model.addAttribute("currentPage", page);
        model.addAttribute("fullNameFilter", fullName);
        model.addAttribute("emailFilter", email);
        model.addAttribute("companyFilter", company);
        model.addAttribute("roleFilter", role);
        model.addAttribute("enabledFilter", enabled);
        model.addAttribute("accountLockedFilter", accountLocked);
        model.addAttribute("allCompanies", companies);
        model.addAttribute("allRoles", roles);

        return "user/fragments/user-list";
    }

    /**
     * Refresh entire user management content including statistics (HTMX endpoint)
     */
    @GetMapping("/refresh")
    @Operation(summary = "Refresh user management content", description = "Refresh entire user management page content including statistics")
    public String refreshUserManagement(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(required = false) String fullName,
            @RequestParam(required = false) String email,
            @RequestParam(required = false) String company,
            @RequestParam(required = false) String role,
            @RequestParam(required = false) Boolean enabled,
            @RequestParam(required = false) Boolean accountLocked,
            Model model) {

        LOGGER.debug("Refreshing user management content - Page: {}", page);

        // Convert role string to ERole enum
        ERole roleEnum = null;
        if (role != null && !role.trim().isEmpty()) {
            try {
                roleEnum = ERole.valueOf(role);
            } catch (IllegalArgumentException e) {
                LOGGER.warn("Invalid role parameter: {}", role);
            }
        }

        Pageable pageable = PageRequest.of(page - 1, 10, Sort.by("fullName").ascending());
        Page<User> users = userService.getUsersWithFilters(fullName, email, company, roleEnum, enabled, accountLocked, pageable);

        // Get filter data for dropdowns
        List<String> companies = userService.getDistinctCompanies();
        List<ERole> roles = userService.getDistinctRoles();

        // Get user statistics for cards
        long totalUsers = userService.getTotalUsersCount();
        long activeUsers = userService.getActiveUsersCount();
        long inactiveUsers = userService.getInactiveUsersCount();
        long lockedUsers = userService.getLockedUsersCount();
        long usersWithoutCompany = userService.getUsersWithoutCompanyCount();

        model.addAttribute("users", users);
        model.addAttribute("currentPage", page);
        model.addAttribute("fullNameFilter", fullName);
        model.addAttribute("emailFilter", email);
        model.addAttribute("companyFilter", company);
        model.addAttribute("roleFilter", role);
        model.addAttribute("enabledFilter", enabled);
        model.addAttribute("accountLockedFilter", accountLocked);
        model.addAttribute("allCompanies", companies);
        model.addAttribute("allRoles", roles);

        // Add statistics for cards
        model.addAttribute("totalUsersCount", totalUsers);
        model.addAttribute("activeUsersCount", activeUsers);
        model.addAttribute("inactiveUsersCount", inactiveUsers);
        model.addAttribute("lockedUsersCount", lockedUsers);
        model.addAttribute("usersWithoutCompanyCount", usersWithoutCompany);

        return "user/fragments/user-content :: userContent";
    }

    /**
     * Clear filters endpoint (HTMX endpoint)
     */
    @GetMapping("/clearFilters")
    @Operation(summary = "Clear user filters", description = "Clear all filters and return to first page")
    public String clearUserFilters(Model model) {
        LOGGER.debug("Clear user filters endpoint called");

        // Call the main list method with cleared filter values
        return getUserList(1, null, null, null, null, null, null, model);
    }

    /**
     * Get user form for create/edit (HTMX endpoint)
     */
    @GetMapping("/form")
    @Operation(summary = "Get user form", description = "Get user form for create or edit")
    public String getUserForm(
            @RequestParam(required = false) Integer id,
            Model model) {

        if (id != null) {
            LOGGER.debug("Getting user form for edit - ID: {}", id);
            User user = userService.findById(id)
                    .orElseThrow(() -> new RuntimeException("User not found with id: " + id));
            EditUserDto editUserDto = userService.toEditUserDto(user);
            model.addAttribute("editUserDto", editUserDto);
            model.addAttribute("isEdit", true);
        } else {
            LOGGER.debug("Getting user form for create");
            model.addAttribute("createUserDto", new CreateUserDto("", "", "", null, "", "", "", "", "", "", "", false, null));
            model.addAttribute("isEdit", false);
        }

        // Add current user information for role validation
        User currentUser = userService.getCurrentUser();
        boolean isCurrentUserAdmin = false;
        if (currentUser != null) {
            isCurrentUserAdmin = currentUser.getRoles().stream()
                    .anyMatch(role -> role.getName() == ERole.ROLE_ADMIN);
        }
        model.addAttribute("isCurrentUserAdmin", isCurrentUserAdmin);

        // Add reference data
        List<Country> countries = countryService.getSortedCountryList();
        List<Language> languages = languageService.getSortedLanguageList();
        List<WorkFormat> workFormats = workFormatService.getSortedWorkFormatList();
        List<Company> companies = companyService.getAllCompaniesForDropdown();
        List<Role> roles = userService.getAllRoles().stream()
                .filter(role -> role.getName() != ERole.ROLE_ADMIN)
                .toList();

        model.addAttribute("allCountries", countries);
        model.addAttribute("allLanguages", languages);
        model.addAttribute("allWorkFormats", workFormats);
        model.addAttribute("allCompanies", companies);
        model.addAttribute("allRoles", roles);

        return "user/fragments/user-form :: userForm";
    }

    /**
     * Create a new user (HTMX endpoint)
     */
    @PostMapping("/create")
    @Operation(summary = "Create user", description = "Create a new user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid user data"),
        @ApiResponse(responseCode = "403", description = "Access denied - admin role required")
    })
    public ResponseEntity<GenericModalMessage> createUser(
            @Valid @ModelAttribute CreateUserDto createUserDto,
            BindingResult bindingResult,
            HttpServletRequest request,
            Locale locale) {

        LOGGER.debug("Creating new user with email: {}", createUserDto.email());
        LOGGER.debug("Received CreateUserDto: {}", createUserDto);

        // Debug: Log all request parameters
        LOGGER.debug("Request parameters:");
        request.getParameterMap().forEach((key, values) ->
            LOGGER.debug("  {}: {}", key, String.join(", ", values))
        );

        if (bindingResult.hasErrors()) {
            // Get localized error messages
            String errorMessage = bindingResult.getAllErrors().stream()
                    .map(error -> {
                        if (error.getDefaultMessage() != null && error.getDefaultMessage().startsWith("{") && error.getDefaultMessage().endsWith("}")) {
                            // This is a message key, resolve it
                            String messageKey = error.getDefaultMessage().substring(1, error.getDefaultMessage().length() - 1);
                            return messageSource.getMessage(messageKey, null, error.getDefaultMessage(), locale);
                        }
                        return error.getDefaultMessage();
                    })
                    .collect(Collectors.joining(", "));

            GenericModalMessage errorResponse = new GenericModalMessage(
                messageSource.getMessage("toast.user.validation.title", null, locale),
                errorMessage,
                Constants.ICON_WARNING
            );
            return ResponseEntity.badRequest().body(errorResponse);
        }

        try {
            userService.createUser(createUserDto);

            GenericModalMessage successResponse = new GenericModalMessage(
                messageSource.getMessage("alert.success.title", null, locale),
                messageSource.getMessage("user.created.message", null, locale),
                Constants.ICON_SUCCESS
            );
            return ResponseEntity.ok(successResponse);

        } catch (Exception e) {
            LOGGER.error("Error creating user: {}", e.getMessage(), e);

            String errorMessage = e.getMessage();

            // Handle specific database errors
            if (errorMessage != null) {
                if (errorMessage.contains("Cannot insert the value NULL into column 'id'") ||
                    errorMessage.contains("IDENTITY")) {
                    // This is a server error
                    errorMessage = messageSource.getMessage("alert.error.server", null, locale);
                    GenericModalMessage errorResponse = new GenericModalMessage(
                        messageSource.getMessage("alert.error.title", null, locale),
                        errorMessage,
                        Constants.ICON_ERROR
                    );
                    return ResponseEntity.status(500).body(errorResponse);
                } else if (errorMessage.contains("already exists")) {
                    // This is a business logic error (user input issue)
                    errorMessage = messageSource.getMessage("alert.error.email.exists", null, locale);
                    GenericModalMessage errorResponse = new GenericModalMessage(
                        messageSource.getMessage("alert.error.title", null, locale),
                        errorMessage,
                        Constants.ICON_ERROR
                    );
                    return ResponseEntity.badRequest().body(errorResponse);
                } else if (errorMessage.contains("constraint")) {
                    // This is a validation error
                    errorMessage = messageSource.getMessage("alert.error.validation", null, locale);
                    GenericModalMessage errorResponse = new GenericModalMessage(
                        messageSource.getMessage("alert.warning.title", null, locale),
                        errorMessage,
                        Constants.ICON_WARNING
                    );
                    return ResponseEntity.badRequest().body(errorResponse);
                }
            }

            // Generic server error
            errorMessage = messageSource.getMessage("alert.error.unknown", null, locale);
            GenericModalMessage errorResponse = new GenericModalMessage(
                messageSource.getMessage("alert.error.title", null, locale),
                errorMessage,
                Constants.ICON_ERROR
            );
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * Update an existing user (HTMX endpoint)
     */
    @PostMapping("/update")
    @Operation(summary = "Update user", description = "Update an existing user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid user data"),
        @ApiResponse(responseCode = "403", description = "Access denied - admin role required")
    })
    public ResponseEntity<GenericModalMessage> updateUser(
            @Valid @ModelAttribute EditUserDto editUserDto,
            BindingResult bindingResult,
            Locale locale) {

        LOGGER.debug("Updating user with ID: {}", editUserDto.id());

        if (bindingResult.hasErrors()) {
            // Get localized error messages
            String errorMessage = bindingResult.getAllErrors().stream()
                    .map(error -> {
                        if (error.getDefaultMessage() != null && error.getDefaultMessage().startsWith("{") && error.getDefaultMessage().endsWith("}")) {
                            // This is a message key, resolve it
                            String messageKey = error.getDefaultMessage().substring(1, error.getDefaultMessage().length() - 1);
                            return messageSource.getMessage(messageKey, null, error.getDefaultMessage(), locale);
                        }
                        return error.getDefaultMessage();
                    })
                    .collect(Collectors.joining(", "));

            GenericModalMessage errorResponse = new GenericModalMessage(
                messageSource.getMessage("alert.warning.title", null, locale),
                errorMessage,
                Constants.ICON_WARNING
            );
            return ResponseEntity.badRequest().body(errorResponse);
        }

        try {
            userService.updateUserAdmin(editUserDto);

            GenericModalMessage successResponse = new GenericModalMessage(
                messageSource.getMessage("alert.success.title", null, locale),
                messageSource.getMessage("user.updated.message", null, locale),
                Constants.ICON_SUCCESS
            );
            return ResponseEntity.ok(successResponse);

        } catch (RuntimeException e) {
            LOGGER.error("Error updating user: {}", e.getMessage());

            String errorMessage = e.getMessage();
            // Handle specific error types for update
            if (errorMessage != null && errorMessage.contains("already exists")) {
                errorMessage = messageSource.getMessage("alert.error.email.exists", null, locale);
            } else if (errorMessage == null) {
                errorMessage = messageSource.getMessage("alert.error.unknown", null, locale);
            }

            GenericModalMessage errorResponse = new GenericModalMessage(
                messageSource.getMessage("alert.error.title", null, locale),
                errorMessage,
                Constants.ICON_ERROR
            );
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * Delete a user by email (HTMX endpoint)
     */
    @DeleteMapping("/email/{email}")
    @Operation(summary = "Delete user by email", description = "Delete a user by email address")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User deleted successfully"),
        @ApiResponse(responseCode = "400", description = "Cannot delete user"),
        @ApiResponse(responseCode = "403", description = "Access denied - admin role required")
    })
    public ResponseEntity<GenericModalMessage> deleteUserByEmail(
            @PathVariable String email,
            Locale locale) {

        LOGGER.debug("Deleting user with email: {}", email);

        try {
            userService.deleteUserByEmail(email);

            GenericModalMessage successResponse = new GenericModalMessage(
                messageSource.getMessage("modal.title.delete.success", null, locale),
                messageSource.getMessage("user.delete.success", null, locale),
                Constants.ICON_SUCCESS
            );
            return ResponseEntity.ok(successResponse);

        } catch (RuntimeException e) {
            LOGGER.error("Error deleting user: {}", e.getMessage());

            GenericModalMessage errorResponse = new GenericModalMessage(
                messageSource.getMessage("modal.title.delete.error", null, locale),
                e.getMessage(),
                Constants.ICON_ERROR
            );
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * Deactivate a user by email (HTMX endpoint)
     */
    @PutMapping("/deactivate/{email}")
    @ResponseBody
    @Operation(summary = "Deactivate user", description = "Deactivate a user account")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User deactivated successfully"),
        @ApiResponse(responseCode = "400", description = "Error deactivating user"),
        @ApiResponse(responseCode = "403", description = "Access denied - admin role required")
    })
    public ResponseEntity<GenericModalMessage> deactivateUserByEmail(
            @PathVariable String email,
            Locale locale) {

        LOGGER.debug("Deactivating user with email: {}", email);

        try {
            userService.deactivateUserByEmail(email);

            GenericModalMessage successResponse = new GenericModalMessage(
                messageSource.getMessage("modal.title.save.success", null, locale),
                messageSource.getMessage("user.deactivate.success", null, locale),
                Constants.ICON_SUCCESS
            );
            return ResponseEntity.ok(successResponse);

        } catch (RuntimeException e) {
            LOGGER.error("Error deactivating user: {}", e.getMessage());

            GenericModalMessage errorResponse = new GenericModalMessage(
                messageSource.getMessage("modal.title.save.error", null, locale),
                e.getMessage(),
                Constants.ICON_ERROR
            );
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * Activate a user by email (HTMX endpoint)
     */
    @PutMapping("/activate/{email}")
    @ResponseBody
    @Operation(summary = "Activate user", description = "Activate a user account")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User activated successfully"),
        @ApiResponse(responseCode = "400", description = "Error activating user"),
        @ApiResponse(responseCode = "403", description = "Access denied - admin role required")
    })
    public ResponseEntity<GenericModalMessage> activateUserByEmail(
            @PathVariable String email,
            Locale locale) {

        LOGGER.debug("Activating user with email: {}", email);

        try {
            userService.activateUserByEmail(email);

            GenericModalMessage successResponse = new GenericModalMessage(
                messageSource.getMessage("modal.title.save.success", null, locale),
                messageSource.getMessage("user.activate.success", null, locale),
                Constants.ICON_SUCCESS
            );
            return ResponseEntity.ok(successResponse);

        } catch (RuntimeException e) {
            LOGGER.error("Error activating user: {}", e.getMessage());

            GenericModalMessage errorResponse = new GenericModalMessage(
                messageSource.getMessage("modal.title.save.error", null, locale),
                e.getMessage(),
                Constants.ICON_ERROR
            );
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * Lock a user by email (HTMX endpoint)
     */
    @PutMapping("/lock/{email}")
    @ResponseBody
    @Operation(summary = "Lock user", description = "Lock a user account")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User locked successfully"),
        @ApiResponse(responseCode = "400", description = "Error locking user"),
        @ApiResponse(responseCode = "403", description = "Access denied - admin role required")
    })
    public ResponseEntity<GenericModalMessage> lockUserByEmail(
            @PathVariable String email,
            Locale locale) {

        LOGGER.debug("Locking user with email: {}", email);

        try {
            userService.lockUserByEmail(email);

            GenericModalMessage successResponse = new GenericModalMessage(
                messageSource.getMessage("modal.title.save.success", null, locale),
                messageSource.getMessage("user.lock.success", null, locale),
                Constants.ICON_SUCCESS
            );
            return ResponseEntity.ok(successResponse);

        } catch (RuntimeException e) {
            LOGGER.error("Error locking user: {}", e.getMessage());

            GenericModalMessage errorResponse = new GenericModalMessage(
                messageSource.getMessage("modal.title.save.error", null, locale),
                e.getMessage(),
                Constants.ICON_ERROR
            );
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * Unlock a user by email (HTMX endpoint)
     */
    @PutMapping("/unlock/{email}")
    @ResponseBody
    @Operation(summary = "Unlock user", description = "Unlock a user account")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User unlocked successfully"),
        @ApiResponse(responseCode = "400", description = "Error unlocking user"),
        @ApiResponse(responseCode = "403", description = "Access denied - admin role required")
    })
    public ResponseEntity<GenericModalMessage> unlockUserByEmail(
            @PathVariable String email,
            Locale locale) {

        LOGGER.debug("Unlocking user with email: {}", email);

        try {
            userService.unlockUserByEmail(email);

            GenericModalMessage successResponse = new GenericModalMessage(
                messageSource.getMessage("modal.title.save.success", null, locale),
                messageSource.getMessage("user.unlock.success", null, locale),
                Constants.ICON_SUCCESS
            );
            return ResponseEntity.ok(successResponse);

        } catch (RuntimeException e) {
            LOGGER.error("Error unlocking user: {}", e.getMessage());

            GenericModalMessage errorResponse = new GenericModalMessage(
                messageSource.getMessage("modal.title.save.error", null, locale),
                e.getMessage(),
                Constants.ICON_ERROR
            );
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * API endpoint to get all users (for external API access)
     */
    @GetMapping("/api")
    @ResponseBody
    @Operation(summary = "Get all users API", description = "Get paginated list of all users via API")
    public ResponseEntity<Page<UserListDto>> getAllUsersApi(
            @Parameter(description = "Pagination parameters") @PageableDefault(size = 10) Pageable pageable,
            @RequestParam(required = false) String fullName,
            @RequestParam(required = false) String email,
            @RequestParam(required = false) String company,
            @RequestParam(required = false) String role,
            @RequestParam(required = false) Boolean enabled,
            @RequestParam(required = false) Boolean accountLocked) {

        // Convert role string to ERole enum
        ERole roleEnum = null;
        if (role != null && !role.trim().isEmpty()) {
            try {
                roleEnum = ERole.valueOf(role);
            } catch (IllegalArgumentException e) {
                LOGGER.warn("Invalid role parameter: {}", role);
            }
        }

        Page<User> users = userService.getUsersWithFilters(fullName, email, company, roleEnum, enabled, accountLocked, pageable);
        Page<UserListDto> userDtos = users.map(userService::toUserListDto);
        return ResponseEntity.ok(userDtos);
    }
}
