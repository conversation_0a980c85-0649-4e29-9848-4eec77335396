package ag.fuel.jobify.company.repository;

import ag.fuel.jobify.company.entity.Company;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CompanyRepository extends JpaRepository<Company, Long> {

    /**
     * Find company by TAX number (unique identifier)
     */
    Optional<Company> findByTaxNumber(String taxNumber);

    /**
     * Find company by company name
     */
    Optional<Company> findByCompanyName(String companyName);

    /**
     * Check if company exists by TAX number (for validation)
     */
    boolean existsByTaxNumber(String taxNumber);

    /**
     * Check if company exists by TAX number excluding specific ID (for updates)
     */
    boolean existsByTaxNumberAndIdNot(String taxNumber, Long id);

    /**
     * Count companies by enabled status
     */
    long countByEnabled(boolean enabled);

    /**
     * Count companies by locked status
     */
    long countByLocked(boolean locked);

    /**
     * Find companies with filtering support
     */
    @Query("SELECT c FROM Company c WHERE " +
           "(:companyName IS NULL OR LOWER(c.companyName) LIKE LOWER(CONCAT('%', :companyName, '%'))) AND " +
           "(:country IS NULL OR LOWER(c.country) LIKE LOWER(CONCAT('%', :country, '%'))) AND " +
           "(:taxNumber IS NULL OR LOWER(c.taxNumber) LIKE LOWER(CONCAT('%', :taxNumber, '%'))) AND " +
           "(:enabled IS NULL OR c.enabled = :enabled) AND " +
           "(:locked IS NULL OR c.locked = :locked) " +
           "ORDER BY c.companyName ASC")
    Page<Company> findCompaniesWithFilters(
            @Param("companyName") String companyName,
            @Param("country") String country,
            @Param("taxNumber") String taxNumber,
            @Param("enabled") Boolean enabled,
            @Param("locked") Boolean locked,
            Pageable pageable);

    /**
     * Get all distinct countries for filter dropdown
     */
    @Query("SELECT DISTINCT c.country FROM Company c ORDER BY c.country")
    java.util.List<String> findDistinctCountries();

    /**
     * Find all companies ordered by company name
     */
    Page<Company> findAllByOrderByCompanyNameAsc(Pageable pageable);
}
