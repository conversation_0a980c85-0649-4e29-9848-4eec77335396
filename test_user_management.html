<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .test-result { margin: 10px 0; padding: 10px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { margin: 5px; padding: 10px 15px; }
    </style>
</head>
<body>
    <h1>User Management Sorting and Pagination Test</h1>
    
    <div class="test-section">
        <h2>Test Instructions</h2>
        <div class="info test-result">
            <p>This test page will help verify that the user management sorting and pagination fixes are working correctly.</p>
            <p>Please follow these steps:</p>
            <ol>
                <li>Open the application at <a href="http://localhost:8080" target="_blank">http://localhost:8080</a></li>
                <li>Login with admin credentials</li>
                <li>Navigate to User Management page</li>
                <li>Verify the following:</li>
                <ul>
                    <li><strong>Sorting:</strong> Users should be sorted by Full Name in ascending order (A-Z)</li>
                    <li><strong>Pagination:</strong> Previous/Next buttons should work correctly</li>
                    <li><strong>Page Numbers:</strong> Clicking on page numbers should navigate correctly</li>
                    <li><strong>Filter Preservation:</strong> Filters should be preserved when navigating between pages</li>
                </ul>
            </ol>
        </div>
    </div>

    <div class="test-section">
        <h2>Quick API Tests</h2>
        <p>Click the buttons below to test the API endpoints directly:</p>
        
        <button onclick="testUserList(1)">Test Page 1</button>
        <button onclick="testUserList(2)">Test Page 2</button>
        <button onclick="testUserListWithFilter()">Test with Filter</button>
        
        <div id="test-results"></div>
    </div>

    <script>
        async function testUserList(page) {
            const resultsDiv = document.getElementById('test-results');
            
            try {
                const response = await fetch(`http://localhost:8080/admin/users/list?page=${page}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'text/html',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                if (response.ok) {
                    const html = await response.text();
                    resultsDiv.innerHTML = `
                        <div class="success test-result">
                            <h3>✓ Page ${page} Test Successful</h3>
                            <p>Response received successfully. Check the browser network tab for details.</p>
                            <details>
                                <summary>Response HTML (first 500 chars)</summary>
                                <pre>${html.substring(0, 500)}...</pre>
                            </details>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="error test-result">
                            <h3>✗ Page ${page} Test Failed</h3>
                            <p>Status: ${response.status} ${response.statusText}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error test-result">
                        <h3>✗ Page ${page} Test Error</h3>
                        <p>Error: ${error.message}</p>
                        <p>Make sure the application is running on localhost:8080 and you're logged in as admin.</p>
                    </div>
                `;
            }
        }

        async function testUserListWithFilter() {
            const resultsDiv = document.getElementById('test-results');
            
            try {
                const response = await fetch(`http://localhost:8080/admin/users/list?page=1&fullName=test`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'text/html',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                if (response.ok) {
                    const html = await response.text();
                    resultsDiv.innerHTML = `
                        <div class="success test-result">
                            <h3>✓ Filter Test Successful</h3>
                            <p>Response received successfully with fullName filter.</p>
                            <details>
                                <summary>Response HTML (first 500 chars)</summary>
                                <pre>${html.substring(0, 500)}...</pre>
                            </details>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="error test-result">
                            <h3>✗ Filter Test Failed</h3>
                            <p>Status: ${response.status} ${response.statusText}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error test-result">
                        <h3>✗ Filter Test Error</h3>
                        <p>Error: ${error.message}</p>
                        <p>Make sure the application is running on localhost:8080 and you're logged in as admin.</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
